﻿时间窗口,策略类型,优化方法,总收益率,年化收益率,最大回撤,夏普比率,胜率,最终价值,参数
1年,linear,differential_evolution,0.1898,0.0985,-0.2781,0.3285,0.4902,1189769,"{'sell_threshold': np.float64(0.22808114266887441), 'buy_threshold': np.float64(0.6381252020185761), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.08)}"
1年,nonlinear,differential_evolution,0.1833,0.0953,-0.1533,0.4556,0.4484,1183325,"{'sell_threshold': np.float64(0.4573901758707996), 'buy_threshold': np.float64(0.8), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.053839848153868164), 'power': np.float64(3.108743045367233)}"
1年,sigmoid,differential_evolution,0.2119,0.1095,-0.2882,0.3520,0.5039,1211855,"{'sell_center': np.float64(0.2013815411417457), 'buy_center': np.float64(0.7000432186529097), 'sell_ratio': np.float64(0.14323314438838633), 'buy_ratio': np.float64(0.07765535668339851), 'steepness': np.float64(21.15400909031742)}"
3年,linear,differential_evolution,0.2026,0.1049,-0.1855,0.4371,0.4811,1202633,"{'sell_threshold': np.float64(0.26559129155297945), 'buy_threshold': np.float64(0.8), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.050196837601325414)}"
3年,nonlinear,differential_evolution,0.2071,0.1072,-0.1734,0.4592,0.4634,1207132,"{'sell_threshold': np.float64(0.3861242228713354), 'buy_threshold': np.float64(0.8), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.06985122258713042), 'power': np.float64(1.9276063788874236)}"
3年,sigmoid,differential_evolution,0.2205,0.1138,-0.2765,0.3736,0.4833,1220526,"{'sell_center': np.float64(0.20008876652462554), 'buy_center': np.float64(0.7368204721237155), 'sell_ratio': np.float64(0.13921054709703243), 'buy_ratio': np.float64(0.07760165369116157), 'steepness': np.float64(24.983182292746818)}"
5年,linear,differential_evolution,0.2026,0.1049,-0.1855,0.4371,0.4811,1202633,"{'sell_threshold': np.float64(0.26559129155297945), 'buy_threshold': np.float64(0.8), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.050196837601325414)}"
5年,nonlinear,differential_evolution,0.2071,0.1072,-0.1734,0.4592,0.4634,1207132,"{'sell_threshold': np.float64(0.3861242228713354), 'buy_threshold': np.float64(0.8), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.06985122258713042), 'power': np.float64(1.9276063788874236)}"
5年,sigmoid,differential_evolution,0.2205,0.1138,-0.2765,0.3736,0.4833,1220526,"{'sell_center': np.float64(0.20008876652462554), 'buy_center': np.float64(0.7368204721237155), 'sell_ratio': np.float64(0.13921054709703243), 'buy_ratio': np.float64(0.07760165369116157), 'steepness': np.float64(24.983182292746818)}"
