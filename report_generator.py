import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ReportGenerator:
    """回测报告生成器"""
    
    def __init__(self, optimization_results, data, benchmark_return=None):
        """
        初始化报告生成器
        
        Args:
            optimization_results: 优化结果字典
            data: 原始数据
            benchmark_return: 基准收益率
        """
        self.results = optimization_results
        self.data = data
        self.benchmark_return = benchmark_return
        
    def generate_summary_table(self):
        """生成结果汇总表"""
        summary_data = []
        
        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result:
                    summary_data.append({
                        '时间窗口': window,
                        '策略类型': strategy_name,
                        '优化方法': strategy_data['method'],
                        '总收益率': f"{result['total_return']:.4f}",
                        '年化收益率': f"{result['annual_return']:.4f}",
                        '最大回撤': f"{result['max_drawdown']:.4f}",
                        '夏普比率': f"{result['sharpe_ratio']:.4f}",
                        '胜率': f"{result['win_rate']:.4f}",
                        '最终价值': f"{result['final_value']:.0f}",
                        '参数': str(result['params'])
                    })
        
        summary_df = pd.DataFrame(summary_data)
        return summary_df
    
    def plot_equity_curves(self, save_path='equity_curves.png'):
        """绘制净值曲线图"""
        fig, axes = plt.subplots(len(self.results), 1, figsize=(15, 5*len(self.results)))
        if len(self.results) == 1:
            axes = [axes]
        
        for i, (window, strategies) in enumerate(self.results.items()):
            ax = axes[i]
            
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result and 'daily_records' in result:
                    daily_records = result['daily_records']
                    ax.plot(daily_records['日期'], daily_records['总价值'], 
                           label=f"{strategy_name} (收益率: {result['total_return']:.2%})",
                           linewidth=2)
            
            # 绘制基准线（初始价值）
            ax.axhline(y=1000000, color='gray', linestyle='--', alpha=0.7, label='初始价值')
            
            ax.set_title(f'{window} 时间窗口 - 净值曲线', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('总价值 (元)')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 格式化y轴
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/10000:.0f}万'))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig
    
    def plot_drawdown_analysis(self, save_path='drawdown_analysis.png'):
        """绘制回撤分析图"""
        fig, axes = plt.subplots(len(self.results), 1, figsize=(15, 4*len(self.results)))
        if len(self.results) == 1:
            axes = [axes]
        
        for i, (window, strategies) in enumerate(self.results.items()):
            ax = axes[i]
            
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result and 'daily_records' in result:
                    daily_records = result['daily_records']
                    ax.fill_between(daily_records['日期'], daily_records['回撤'], 0,
                                   alpha=0.6, label=f"{strategy_name} (最大回撤: {result['max_drawdown']:.2%})")
            
            ax.set_title(f'{window} 时间窗口 - 回撤分析', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('回撤比例')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 格式化y轴为百分比
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig
    
    def plot_strategy_signals(self, window='1年', strategy='linear', save_path='strategy_signals.png'):
        """绘制策略信号图"""
        if window not in self.results or strategy not in self.results[window]:
            print(f"未找到 {window} - {strategy} 的结果")
            return None
        
        result = self.results[window][strategy]['result']
        if not result or 'daily_records' not in result:
            print("没有找到详细记录")
            return None
        
        daily_records = result['daily_records']
        
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        
        # 第一个子图：价格和分位数
        ax1 = axes[0]
        ax1_twin = ax1.twinx()
        
        ax1.plot(daily_records['日期'], daily_records['价格'], 'b-', label='ETF价格', linewidth=2)
        ax1_twin.plot(daily_records['日期'], daily_records['分位数'], 'r-', label='股债利差分位数', linewidth=2)
        
        ax1.set_ylabel('ETF价格', color='b')
        ax1_twin.set_ylabel('分位数', color='r')
        ax1.set_title(f'{window} - {strategy} 策略信号分析', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # 第二个子图：操作信号
        ax2 = axes[1]
        buy_signals = daily_records[daily_records['操作比例'] > 0]
        sell_signals = daily_records[daily_records['操作比例'] < 0]
        
        ax2.scatter(buy_signals['日期'], buy_signals['操作比例'], 
                   color='green', alpha=0.6, label='买入信号', s=20)
        ax2.scatter(sell_signals['日期'], sell_signals['操作比例'], 
                   color='red', alpha=0.6, label='卖出信号', s=20)
        ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
        
        ax2.set_ylabel('操作比例')
        ax2.set_title('买卖信号')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 第三个子图：资产配置
        ax3 = axes[2]
        ax3.fill_between(daily_records['日期'], 0, daily_records['现金'], 
                        alpha=0.6, label='现金', color='gold')
        ax3.fill_between(daily_records['日期'], daily_records['现金'], daily_records['总价值'], 
                        alpha=0.6, label='ETF价值', color='lightblue')
        
        ax3.set_xlabel('日期')
        ax3.set_ylabel('价值 (元)')
        ax3.set_title('资产配置变化')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/10000:.0f}万'))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig
    
    def create_interactive_dashboard(self, save_path='dashboard.html'):
        """创建交互式仪表板"""
        # 准备数据
        all_data = []
        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result and 'daily_records' in result:
                    daily_records = result['daily_records'].copy()
                    daily_records['窗口'] = window
                    daily_records['策略'] = strategy_name
                    all_data.append(daily_records)
        
        if not all_data:
            print("没有可用的数据创建仪表板")
            return None
        
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('净值曲线', '回撤分析', '分位数分布', '收益率分布'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 净值曲线
        for window in combined_df['窗口'].unique():
            for strategy in combined_df['策略'].unique():
                data_subset = combined_df[(combined_df['窗口'] == window) & (combined_df['策略'] == strategy)]
                if not data_subset.empty:
                    fig.add_trace(
                        go.Scatter(
                            x=data_subset['日期'],
                            y=data_subset['总价值'],
                            name=f'{window}-{strategy}',
                            line=dict(width=2)
                        ),
                        row=1, col=1
                    )
        
        # 回撤分析
        for window in combined_df['窗口'].unique():
            for strategy in combined_df['策略'].unique():
                data_subset = combined_df[(combined_df['窗口'] == window) & (combined_df['策略'] == strategy)]
                if not data_subset.empty:
                    fig.add_trace(
                        go.Scatter(
                            x=data_subset['日期'],
                            y=data_subset['回撤'],
                            name=f'{window}-{strategy}',
                            fill='tonexty',
                            showlegend=False
                        ),
                        row=1, col=2
                    )
        
        # 分位数分布
        fig.add_trace(
            go.Histogram(
                x=combined_df['分位数'],
                nbinsx=50,
                name='分位数分布',
                showlegend=False
            ),
            row=2, col=1
        )
        
        # 收益率分布
        fig.add_trace(
            go.Histogram(
                x=combined_df['收益率'],
                nbinsx=50,
                name='收益率分布',
                showlegend=False
            ),
            row=2, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title_text="股债利差回测策略分析仪表板",
            title_x=0.5,
            height=800,
            showlegend=True
        )
        
        # 保存HTML文件
        fig.write_html(save_path)
        print(f"交互式仪表板已保存至: {save_path}")
        
        return fig
    
    def generate_full_report(self, output_dir='reports'):
        """生成完整报告"""
        import os
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        print("正在生成完整回测报告...")
        
        # 1. 生成汇总表
        summary_df = self.generate_summary_table()
        summary_df.to_csv(f'{output_dir}/summary_table.csv', index=False, encoding='utf-8-sig')
        print("✓ 汇总表已生成")
        
        # 2. 生成净值曲线图
        self.plot_equity_curves(f'{output_dir}/equity_curves.png')
        print("✓ 净值曲线图已生成")
        
        # 3. 生成回撤分析图
        self.plot_drawdown_analysis(f'{output_dir}/drawdown_analysis.png')
        print("✓ 回撤分析图已生成")
        
        # 4. 为每个策略生成信号图
        for window, strategies in self.results.items():
            for strategy_name in strategies.keys():
                self.plot_strategy_signals(
                    window, strategy_name, 
                    f'{output_dir}/signals_{window}_{strategy_name}.png'
                )
        print("✓ 策略信号图已生成")
        
        # 5. 生成交互式仪表板
        self.create_interactive_dashboard(f'{output_dir}/dashboard.html')
        print("✓ 交互式仪表板已生成")
        
        # 6. 生成文字报告
        self.generate_text_report(f'{output_dir}/report.txt')
        print("✓ 文字报告已生成")
        
        print(f"\n完整报告已生成至目录: {output_dir}")
        return summary_df
    
    def generate_text_report(self, save_path='report.txt'):
        """生成文字报告"""
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("股债利差回测策略分析报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 数据概览
            f.write("1. 数据概览\n")
            f.write("-" * 30 + "\n")
            f.write(f"回测时间范围: {self.data['日期'].min()} 至 {self.data['日期'].max()}\n")
            f.write(f"总交易日数: {len(self.data)} 天\n")
            f.write(f"股债利差范围: {self.data['股债利差'].min():.4f} 至 {self.data['股债利差'].max():.4f}\n")
            f.write(f"ETF价格范围: {self.data['价格'].min():.2f} 至 {self.data['价格'].max():.2f}\n\n")
            
            # 策略结果汇总
            f.write("2. 策略结果汇总\n")
            f.write("-" * 30 + "\n")
            
            best_overall = None
            best_score = -np.inf
            
            for window, strategies in self.results.items():
                f.write(f"\n{window} 时间窗口:\n")
                
                for strategy_name, strategy_data in strategies.items():
                    result = strategy_data['result']
                    if result:
                        score = result['total_return'] - abs(result['max_drawdown']) * 0.1
                        if score > best_score:
                            best_score = score
                            best_overall = (window, strategy_name, result)
                        
                        f.write(f"  {strategy_name} 策略:\n")
                        f.write(f"    总收益率: {result['total_return']:.4f} ({result['total_return']:.2%})\n")
                        f.write(f"    年化收益率: {result['annual_return']:.4f} ({result['annual_return']:.2%})\n")
                        f.write(f"    最大回撤: {result['max_drawdown']:.4f} ({result['max_drawdown']:.2%})\n")
                        f.write(f"    夏普比率: {result['sharpe_ratio']:.4f}\n")
                        f.write(f"    胜率: {result['win_rate']:.4f} ({result['win_rate']:.2%})\n")
                        f.write(f"    最终价值: {result['final_value']:.0f} 元\n")
                        f.write(f"    最优参数: {result['params']}\n\n")
            
            # 最佳策略推荐
            if best_overall:
                window, strategy_name, result = best_overall
                f.write("3. 最佳策略推荐\n")
                f.write("-" * 30 + "\n")
                f.write(f"推荐策略: {window} - {strategy_name}\n")
                f.write(f"推荐理由: 综合考虑收益率和风险控制，该策略表现最佳\n\n")
                f.write("详细指标:\n")
                f.write(f"  总收益率: {result['total_return']:.2%}\n")
                f.write(f"  年化收益率: {result['annual_return']:.2%}\n")
                f.write(f"  最大回撤: {result['max_drawdown']:.2%}\n")
                f.write(f"  夏普比率: {result['sharpe_ratio']:.4f}\n")
                f.write(f"  胜率: {result['win_rate']:.2%}\n")
                f.write(f"  最终价值: {result['final_value']:.0f} 元\n")
                f.write(f"  相对初始投资收益: {(result['final_value'] - 1000000):.0f} 元\n\n")
                f.write(f"最优参数设置:\n")
                for param, value in result['params'].items():
                    f.write(f"  {param}: {value}\n")
            
            f.write("\n" + "=" * 60 + "\n")
            f.write("报告生成完成\n")
            f.write("=" * 60 + "\n")

if __name__ == "__main__":
    # 测试报告生成器
    print("报告生成器测试完成")
