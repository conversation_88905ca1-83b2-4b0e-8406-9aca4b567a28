﻿时间窗口,策略类型,优化方法,总收益率,年化收益率,最大回撤,夏普比率,胜率,最终价值,参数
1年,linear,differential_evolution,0.1448,0.0759,-0.0664,0.7621,0.4141,1144792,"{'sell_threshold': np.float64(0.2759566615928561), 'buy_threshold': np.float64(0.9), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.005)}"
1年,linear_aggressive,differential_evolution,0.1830,0.0951,-0.0664,0.7758,0.4000,1183026,"{'sell_threshold': np.float64(0.2332032206519446), 'buy_threshold': np.float64(0.95), 'sell_ratio': np.float64(0.2), 'buy_ratio': np.float64(0.02)}"
1年,nonlinear,differential_evolution,0.1448,0.0759,-0.0664,0.7621,0.4141,1144792,"{'sell_threshold': np.float64(0.27595667774915555), 'buy_threshold': np.float64(0.9), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.005), 'power': np.float64(1.0)}"
1年,sigmoid,differential_evolution,0.1716,0.0894,-0.0664,0.8189,0.4074,1171589,"{'sell_center': np.float64(0.19285379875051795), 'buy_center': np.float64(0.8975325143145608), 'sell_ratio': np.float64(0.14981275559386317), 'buy_ratio': np.float64(0.005261568868081569), 'steepness': np.float64(29.673947054083502)}"
1年,multi_threshold,differential_evolution,0.2078,0.1075,-0.0762,0.7235,0.3974,1207807,"{'sell_threshold_1': np.float64(0.1621639434089054), 'sell_threshold_2': np.float64(0.16993963974422008), 'buy_threshold_1': np.float64(0.8977425168449016), 'buy_threshold_2': np.float64(0.9625814119910611), 'sell_ratio_1': np.float64(0.04706585452899006), 'sell_ratio_2': np.float64(0.15), 'buy_ratio_1': np.float64(0.005), 'buy_ratio_2': np.float64(0.02)}"
1年,dynamic,differential_evolution,0.1583,0.0827,-0.0664,0.6586,0.4034,1158265,"{'base_sell_threshold': np.float64(0.3512377639722962), 'base_buy_threshold': np.float64(0.9), 'base_sell_ratio': np.float64(0.08), 'base_buy_ratio': np.float64(0.01), 'volatility_factor': np.float64(1.2794290839158837)}"
3年,linear,differential_evolution,0.1425,0.0747,-0.0664,0.7608,0.3962,1142505,"{'sell_threshold': np.float64(0.33111917590790946), 'buy_threshold': np.float64(0.8833366824731351), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.006487130292311192)}"
3年,linear_aggressive,differential_evolution,0.1755,0.0914,-0.0664,0.7729,0.4286,1175465,"{'sell_threshold': np.float64(0.261926910981944), 'buy_threshold': np.float64(0.95), 'sell_ratio': np.float64(0.2), 'buy_ratio': np.float64(0.02)}"
3年,nonlinear,differential_evolution,0.1422,0.0745,-0.0664,0.7611,0.3737,1142164,"{'sell_threshold': np.float64(0.3311191772236481), 'buy_threshold': np.float64(0.9), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.00714049461706152), 'power': np.float64(1.0)}"
3年,sigmoid,differential_evolution,0.1755,0.0914,-0.0664,0.8418,0.4125,1175464,"{'sell_center': np.float64(0.1905424604241118), 'buy_center': np.float64(0.8812916746664239), 'sell_ratio': np.float64(0.14896142872014806), 'buy_ratio': np.float64(0.005072880142213271), 'steepness': np.float64(29.778477672805558)}"
3年,multi_threshold,differential_evolution,0.2123,0.1097,-0.0664,0.8032,0.3676,1212284,"{'sell_threshold_1': np.float64(0.10531223651266688), 'sell_threshold_2': np.float64(0.16764393422126994), 'buy_threshold_1': np.float64(0.8966726953985186), 'buy_threshold_2': np.float64(0.974629016555465), 'sell_ratio_1': np.float64(0.03897855719162613), 'sell_ratio_2': np.float64(0.15), 'buy_ratio_1': np.float64(0.005), 'buy_ratio_2': np.float64(0.02)}"
3年,dynamic,differential_evolution,0.1680,0.0876,-0.0664,0.7036,0.3894,1168029,"{'base_sell_threshold': np.float64(0.35950224692308197), 'base_buy_threshold': np.float64(0.9), 'base_sell_ratio': np.float64(0.08), 'base_buy_ratio': np.float64(0.01), 'volatility_factor': np.float64(1.7689137284675382)}"
5年,linear,differential_evolution,0.1425,0.0747,-0.0664,0.7608,0.3962,1142505,"{'sell_threshold': np.float64(0.33111917590790946), 'buy_threshold': np.float64(0.8833366824731351), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.006487130292311192)}"
5年,linear_aggressive,differential_evolution,0.1755,0.0914,-0.0664,0.7729,0.4286,1175465,"{'sell_threshold': np.float64(0.261926910981944), 'buy_threshold': np.float64(0.95), 'sell_ratio': np.float64(0.2), 'buy_ratio': np.float64(0.02)}"
5年,nonlinear,differential_evolution,0.1422,0.0745,-0.0664,0.7611,0.3737,1142164,"{'sell_threshold': np.float64(0.3311191772236481), 'buy_threshold': np.float64(0.9), 'sell_ratio': np.float64(0.15), 'buy_ratio': np.float64(0.00714049461706152), 'power': np.float64(1.0)}"
5年,sigmoid,differential_evolution,0.1755,0.0914,-0.0664,0.8418,0.4125,1175464,"{'sell_center': np.float64(0.1905424604241118), 'buy_center': np.float64(0.8812916746664239), 'sell_ratio': np.float64(0.14896142872014806), 'buy_ratio': np.float64(0.005072880142213271), 'steepness': np.float64(29.778477672805558)}"
5年,multi_threshold,differential_evolution,0.2123,0.1097,-0.0664,0.8032,0.3676,1212284,"{'sell_threshold_1': np.float64(0.10531223651266688), 'sell_threshold_2': np.float64(0.16764393422126994), 'buy_threshold_1': np.float64(0.8966726953985186), 'buy_threshold_2': np.float64(0.974629016555465), 'sell_ratio_1': np.float64(0.03897855719162613), 'sell_ratio_2': np.float64(0.15), 'buy_ratio_1': np.float64(0.005), 'buy_ratio_2': np.float64(0.02)}"
5年,dynamic,differential_evolution,0.1680,0.0876,-0.0664,0.7036,0.3894,1168029,"{'base_sell_threshold': np.float64(0.35950224692308197), 'base_buy_threshold': np.float64(0.9), 'base_sell_ratio': np.float64(0.08), 'base_buy_ratio': np.float64(0.01), 'volatility_factor': np.float64(1.7689137284675382)}"
